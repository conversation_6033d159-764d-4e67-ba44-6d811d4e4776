using Microsoft.CodeAnalysis;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace TestLE.SourceGenerator.Core
{
    public static class CodeGenerator
    {
        public static string GeneratePatchClass(AutoPatchClassInfo classInfo, GeneratorExecutionContext context)
        {
            var sb = new StringBuilder();

            // File header
            sb.AppendLine("// <auto-generated />");
            sb.AppendLine("// This file was generated by the TestLE AutoPatch Source Generator");
            sb.AppendLine();
            sb.AppendLine("using System;");
            sb.AppendLine("using HarmonyLib;");
            sb.AppendLine("using Il2Cpp;");
            sb.AppendLine("using Il2CppInterop.Runtime.InteropTypes.Arrays;");
            sb.AppendLine();

            // Namespace
            if (!string.IsNullOrEmpty(classInfo.Namespace))
            {
                sb.AppendLine($"namespace {classInfo.Namespace}");
                sb.AppendLine("{");
            }

            // Generate patch classes for each method
            foreach (var method in classInfo.PatchMethods)
            {
                GeneratePatchMethod(sb, classInfo, method, context);
                sb.AppendLine();
            }

            // Close namespace
            if (!string.IsNullOrEmpty(classInfo.Namespace))
            {
                sb.AppendLine("}");
            }

            return sb.ToString();
        }

        private static void GeneratePatchMethod(StringBuilder sb, AutoPatchClassInfo classInfo, PatchMethodInfo method, GeneratorExecutionContext context)
        {
            var targetType = classInfo.TargetType ?? "UnknownType";
            var patchClassName = $"Generated_{classInfo.ClassName}_{method.MethodName}_{method.PatchType}";

            // Class declaration with HarmonyPatch attribute
            sb.AppendLine($"    [HarmonyPatch(typeof({targetType}), \"{method.TargetMethodName}\")]");
            if (method.Priority != 400)
            {
                sb.AppendLine($"    [HarmonyPriority({method.Priority})]");
            }
            sb.AppendLine($"    public static class {patchClassName}");
            sb.AppendLine("    {");

            // Generate the patch method
            var methodSignature = GenerateMethodSignature(method);
            sb.AppendLine($"        [Harmony{method.PatchType}]");
            sb.AppendLine($"        public static {methodSignature}");
            sb.AppendLine("        {");

            // Generate method body
            GenerateMethodBody(sb, classInfo, method);

            sb.AppendLine("        }");
            sb.AppendLine("    }");
        }

        private static string GenerateMethodSignature(PatchMethodInfo method)
        {
            var parameters = new List<string>();

            foreach (var param in method.Parameters)
            {
                var paramStr = GenerateParameterString(param);
                parameters.Add(paramStr);
            }

            var parameterList = string.Join(", ", parameters);
            return $"{method.ReturnType} {method.MethodName}({parameterList})";
        }

        private static string GenerateParameterString(ParameterInfo param)
        {
            var result = "";

            // Add ref/out modifiers
            if (param.ByRef)
            {
                result += param.ParameterType == ParameterType.Result ? "ref " : "out ";
            }

            // Add parameter type
            result += param.Type;

            // Add parameter name with Harmony naming convention
            result += " " + GenerateHarmonyParameterName(param);

            return result;
        }

        private static string GenerateHarmonyParameterName(ParameterInfo param)
        {
            return param.ParameterType switch
            {
                ParameterType.Instance => "__instance",
                ParameterType.Result => "__result",
                ParameterType.OriginalMethod => "__originalMethod",
                ParameterType.RunOriginal => "__runOriginal",
                ParameterType.Exception => "__exception",
                ParameterType.State => "__state",
                ParameterType.OriginalParameter when param.OriginalParameterIndex.HasValue => $"__arg{param.OriginalParameterIndex}",
                ParameterType.OriginalParameter when !string.IsNullOrEmpty(param.OriginalParameterName) => param.OriginalParameterName,
                _ => param.Name
            };
        }

        private static void GenerateMethodBody(StringBuilder sb, AutoPatchClassInfo classInfo, PatchMethodInfo method)
        {
            // Add debug logging if enabled
            if (classInfo.EnableDebugLogging)
            {
                sb.AppendLine($"            MelonLoader.MelonLogger.Msg($\"[AutoPatch] {method.PatchType} patch for {classInfo.TargetType}.{method.TargetMethodName} called\");");
            }

            // Add condition check if specified
            if (!string.IsNullOrEmpty(method.Condition))
            {
                sb.AppendLine($"            if (!({method.Condition}))");
                sb.AppendLine("            {");
                if (method.PatchType == PatchType.Prefix && method.ReturnType == "bool")
                {
                    sb.AppendLine("                return true; // Continue with original method");
                }
                else if (method.ReturnType != "void")
                {
                    sb.AppendLine($"                return default({method.ReturnType});");
                }
                else
                {
                    sb.AppendLine("                return;");
                }
                sb.AppendLine("            }");
                sb.AppendLine();
            }

            // Generate call to original method
            var originalMethodCall = GenerateOriginalMethodCall(classInfo, method);
            sb.AppendLine($"            {originalMethodCall}");
        }

        private static string GenerateOriginalMethodCall(AutoPatchClassInfo classInfo, PatchMethodInfo method)
        {
            var originalClassName = classInfo.ClassName;
            var parameters = method.Parameters
                .Where(p => p.ParameterType != ParameterType.OriginalMethod && p.ParameterType != ParameterType.RunOriginal)
                .Select(p => GenerateHarmonyParameterName(p))
                .ToList();

            var parameterList = string.Join(", ", parameters);

            if (method.ReturnType == "void")
            {
                return $"{originalClassName}.{method.MethodName}({parameterList});";
            }
            else
            {
                return $"return {originalClassName}.{method.MethodName}({parameterList});";
            }
        }

        public static string GenerateRegistrationCode(List<AutoPatchClassInfo> classes)
        {
            if (!classes.Any())
                return "";

            var sb = new StringBuilder();

            sb.AppendLine("// <auto-generated />");
            sb.AppendLine("// This file was generated by the TestLE AutoPatch Source Generator");
            sb.AppendLine();
            sb.AppendLine("using HarmonyLib;");
            sb.AppendLine("using MelonLoader;");
            sb.AppendLine();
            sb.AppendLine("namespace TestLE.Generated");
            sb.AppendLine("{");
            sb.AppendLine("    public static class AutoPatchRegistration");
            sb.AppendLine("    {");
            sb.AppendLine("        public static void RegisterAllPatches(Harmony harmony)");
            sb.AppendLine("        {");
            sb.AppendLine("            MelonLogger.Msg(\"[AutoPatch] Registering generated patches...\");");
            sb.AppendLine();

            foreach (var classInfo in classes)
            {
                sb.AppendLine($"            // Patches for {classInfo.ClassName}");
                foreach (var method in classInfo.PatchMethods)
                {
                    var patchClassName = $"Generated_{classInfo.ClassName}_{method.MethodName}_{method.PatchType}";
                    sb.AppendLine($"            harmony.PatchAll(typeof({classInfo.Namespace}.{patchClassName}));");
                }
                sb.AppendLine();
            }

            sb.AppendLine("            MelonLogger.Msg($\"[AutoPatch] Registered {GetPatchCount()} patches\");");
            sb.AppendLine("        }");
            sb.AppendLine();
            sb.AppendLine("        private static int GetPatchCount()");
            sb.AppendLine("        {");
            sb.AppendLine($"            return {classes.Sum(c => c.PatchMethods.Count)};");
            sb.AppendLine("        }");
            sb.AppendLine("    }");
            sb.AppendLine("}");

            return sb.ToString();
        }
    }
}
