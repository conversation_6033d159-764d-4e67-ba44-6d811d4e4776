using Microsoft.CodeAnalysis;
using Microsoft.CodeAnalysis.CSharp.Syntax;
using System.Collections.Generic;
using System.Linq;

namespace TestLE.SourceGenerator.Core
{
    internal class AutoPatchSyntaxReceiver : ISyntaxReceiver
    {
        public List<ClassDeclarationSyntax> AutoPatchClasses { get; } = new List<ClassDeclarationSyntax>();

        public void OnVisitSyntaxNode(SyntaxNode syntaxNode)
        {
            if (syntaxNode is ClassDeclarationSyntax classDeclaration)
            {
                // Check if the class has the AutoPatch attribute
                if (HasAutoPatchAttribute(classDeclaration))
                {
                    AutoPatchClasses.Add(classDeclaration);
                }
            }
        }

        private bool HasAutoPatchAttribute(ClassDeclarationSyntax classDeclaration)
        {
            return classDeclaration.AttributeLists
                .SelectMany(al => al.Attributes)
                .Any(attr => IsAutoPatchAttribute(attr));
        }

        private bool IsAutoPatchAttribute(AttributeSyntax attribute)
        {
            var name = attribute.Name.ToString();
            return name == "AutoPatch" || 
                   name == "AutoPatchAttribute" ||
                   name.EndsWith(".AutoPatch") ||
                   name.EndsWith(".AutoPatchAttribute");
        }
    }
}
