using Microsoft.CodeAnalysis;
using Microsoft.CodeAnalysis.CSharp;
using Microsoft.CodeAnalysis.CSharp.Syntax;
using Microsoft.CodeAnalysis.Text;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace TestLE.SourceGenerator.Core
{
    [Generator]
    public class PatchSourceGenerator : ISourceGenerator
    {
        private const string AutoPatchAttributeName = "TestLE.SourceGenerator.Attributes.AutoPatchAttribute";
        private const string PrefixAttributeName = "TestLE.SourceGenerator.Attributes.PrefixAttribute";
        private const string PostfixAttributeName = "TestLE.SourceGenerator.Attributes.PostfixAttribute";
        private const string TranspilerAttributeName = "TestLE.SourceGenerator.Attributes.TranspilerAttribute";
        private const string FinalizerAttributeName = "TestLE.SourceGenerator.Attributes.FinalizerAttribute";

        public void Initialize(GeneratorInitializationContext context)
        {
            context.RegisterForSyntaxNotifications(() => new AutoPatchSyntaxReceiver());
        }

        public void Execute(GeneratorExecutionContext context)
        {
            try
            {
                if (!(context.SyntaxReceiver is AutoPatchSyntaxReceiver receiver))
                    return;

                var compilation = context.Compilation;
                var autoPatchClasses = new List<AutoPatchClassInfo>();

                // Process each class marked with AutoPatch
                foreach (var classDeclaration in receiver.AutoPatchClasses)
                {
                    var semanticModel = compilation.GetSemanticModel(classDeclaration.SyntaxTree);
                    var classSymbol = semanticModel.GetDeclaredSymbol(classDeclaration);

                    if (classSymbol == null)
                        continue;

                    var autoPatchInfo = AnalyzeAutoPatchClass(classSymbol, classDeclaration, semanticModel, context);
                    if (autoPatchInfo != null)
                    {
                        autoPatchClasses.Add(autoPatchInfo);
                    }
                }

                // Generate patch code for each class
                foreach (var autoPatchClass in autoPatchClasses)
                {
                    var generatedCode = GeneratePatchClass(autoPatchClass, context);
                    if (!string.IsNullOrEmpty(generatedCode))
                    {
                        var fileName = $"{autoPatchClass.ClassName}_Generated.cs";
                        context.AddSource(fileName, SourceText.From(generatedCode, Encoding.UTF8));
                    }
                }

                // Generate registration code
                var registrationCode = GenerateRegistrationCode(autoPatchClasses);
                if (!string.IsNullOrEmpty(registrationCode))
                {
                    context.AddSource("AutoPatch_Registration.cs", SourceText.From(registrationCode, Encoding.UTF8));
                }
            }
            catch (Exception ex)
            {
                // Report any unexpected errors
                var descriptor = new DiagnosticDescriptor(
                    "AUTOPATCH001",
                    "Source generator error",
                    $"An error occurred in the AutoPatch source generator: {ex.Message}",
                    "AutoPatch",
                    DiagnosticSeverity.Error,
                    isEnabledByDefault: true);

                context.ReportDiagnostic(Diagnostic.Create(descriptor, Location.None));
            }
        }

        private AutoPatchClassInfo? AnalyzeAutoPatchClass(
            INamedTypeSymbol classSymbol,
            ClassDeclarationSyntax classDeclaration,
            SemanticModel semanticModel,
            GeneratorExecutionContext context)
        {
            var autoPatchAttribute = classSymbol.GetAttributes()
                .FirstOrDefault(attr => attr.AttributeClass?.ToDisplayString() == AutoPatchAttributeName);

            if (autoPatchAttribute == null)
                return null;

            var classInfo = new AutoPatchClassInfo
            {
                ClassName = classSymbol.Name,
                Namespace = classSymbol.ContainingNamespace.ToDisplayString(),
                TargetType = ExtractTargetType(autoPatchAttribute),
                Priority = ExtractPriority(autoPatchAttribute),
                EnableDebugLogging = ExtractDebugLogging(autoPatchAttribute),
                ValidateTargetMethods = ExtractValidateTargetMethods(autoPatchAttribute)
            };

            // Analyze patch methods
            foreach (var method in classDeclaration.Members.OfType<MethodDeclarationSyntax>())
            {
                var methodSymbol = semanticModel.GetDeclaredSymbol(method);
                if (methodSymbol == null)
                    continue;

                var patchMethod = AnalyzePatchMethod(methodSymbol, method, semanticModel, context);
                if (patchMethod != null)
                {
                    classInfo.PatchMethods.Add(patchMethod);
                }
            }

            return classInfo.PatchMethods.Any() ? classInfo : null;
        }

        private PatchMethodInfo? AnalyzePatchMethod(
            IMethodSymbol methodSymbol,
            MethodDeclarationSyntax methodDeclaration,
            SemanticModel semanticModel,
            GeneratorExecutionContext context)
        {
            var patchAttributes = methodSymbol.GetAttributes()
                .Where(attr => IsPatchAttribute(attr.AttributeClass?.ToDisplayString()))
                .ToList();

            if (!patchAttributes.Any())
                return null;

            var patchAttribute = patchAttributes.First();
            var patchType = GetPatchType(patchAttribute.AttributeClass?.ToDisplayString());

            var methodInfo = new PatchMethodInfo
            {
                MethodName = methodSymbol.Name,
                PatchType = patchType,
                TargetMethodName = ExtractTargetMethodName(patchAttribute, methodSymbol.Name),
                Priority = ExtractMethodPriority(patchAttribute),
                IsStatic = methodSymbol.IsStatic,
                ReturnType = methodSymbol.ReturnType.ToDisplayString(),
                Parameters = AnalyzeParameters(methodSymbol, methodDeclaration, semanticModel)
            };

            return methodInfo;
        }

        private bool IsPatchAttribute(string? attributeName)
        {
            return attributeName == PrefixAttributeName ||
                   attributeName == PostfixAttributeName ||
                   attributeName == TranspilerAttributeName ||
                   attributeName == FinalizerAttributeName;
        }

        private PatchType GetPatchType(string? attributeName)
        {
            return attributeName switch
            {
                PrefixAttributeName => PatchType.Prefix,
                PostfixAttributeName => PatchType.Postfix,
                TranspilerAttributeName => PatchType.Transpiler,
                FinalizerAttributeName => PatchType.Finalizer,
                _ => PatchType.Postfix
            };
        }

        private string? ExtractTargetType(AttributeData attribute)
        {
            // Try to get TargetType property
            var targetTypeArg = attribute.NamedArguments.FirstOrDefault(arg => arg.Key == "TargetType");
            if (targetTypeArg.Value.Value is INamedTypeSymbol targetType)
            {
                return targetType.ToDisplayString();
            }

            // Try to get TargetTypeName property
            var targetTypeNameArg = attribute.NamedArguments.FirstOrDefault(arg => arg.Key == "TargetTypeName");
            if (targetTypeNameArg.Value.Value is string targetTypeName && !string.IsNullOrEmpty(targetTypeName))
            {
                return targetTypeName;
            }

            // Try constructor arguments
            if (attribute.ConstructorArguments.Length > 0)
            {
                var firstArg = attribute.ConstructorArguments[0];
                if (firstArg.Value is INamedTypeSymbol constructorTargetType)
                {
                    return constructorTargetType.ToDisplayString();
                }
                if (firstArg.Value is string constructorTargetTypeName)
                {
                    return constructorTargetTypeName;
                }
            }

            return null;
        }

        private int ExtractPriority(AttributeData attribute)
        {
            var priorityArg = attribute.NamedArguments.FirstOrDefault(arg => arg.Key == "Priority");
            if (priorityArg.Value.Value is int priority)
            {
                return priority;
            }
            return 400; // Default Harmony priority
        }

        private bool ExtractDebugLogging(AttributeData attribute)
        {
            var debugArg = attribute.NamedArguments.FirstOrDefault(arg => arg.Key == "EnableDebugLogging");
            if (debugArg.Value.Value is bool enableDebug)
            {
                return enableDebug;
            }
            return false;
        }

        private bool ExtractValidateTargetMethods(AttributeData attribute)
        {
            var validateArg = attribute.NamedArguments.FirstOrDefault(arg => arg.Key == "ValidateTargetMethods");
            if (validateArg.Value.Value is bool validate)
            {
                return validate;
            }
            return true;
        }

        private string ExtractTargetMethodName(AttributeData attribute, string methodName)
        {
            var methodNameArg = attribute.NamedArguments.FirstOrDefault(arg => arg.Key == "MethodName");
            if (methodNameArg.Value.Value is string targetMethodName && !string.IsNullOrEmpty(targetMethodName))
            {
                return targetMethodName;
            }

            // Try constructor arguments
            if (attribute.ConstructorArguments.Length > 0)
            {
                var firstArg = attribute.ConstructorArguments[0];
                if (firstArg.Value is string constructorMethodName)
                {
                    return constructorMethodName;
                }
            }

            // Infer from patch method name (remove common prefixes/suffixes)
            return InferTargetMethodName(methodName);
        }

        private string InferTargetMethodName(string patchMethodName)
        {
            // Remove common patch prefixes/suffixes
            var prefixes = new[] { "Patch_", "Prefix_", "Postfix_", "Transpiler_", "Finalizer_" };
            var suffixes = new[] { "_Patch", "_Prefix", "_Postfix", "_Transpiler", "_Finalizer" };

            var result = patchMethodName;

            foreach (var prefix in prefixes)
            {
                if (result.StartsWith(prefix))
                {
                    result = result.Substring(prefix.Length);
                    break;
                }
            }

            foreach (var suffix in suffixes)
            {
                if (result.EndsWith(suffix))
                {
                    result = result.Substring(0, result.Length - suffix.Length);
                    break;
                }
            }

            return result;
        }

        private int ExtractMethodPriority(AttributeData attribute)
        {
            var priorityArg = attribute.NamedArguments.FirstOrDefault(arg => arg.Key == "Priority");
            if (priorityArg.Value.Value is int priority)
            {
                return priority;
            }
            return 400;
        }

        private List<ParameterInfo> AnalyzeParameters(IMethodSymbol method, MethodDeclarationSyntax syntax, SemanticModel model)
        {
            var parameters = new List<ParameterInfo>();

            for (int i = 0; i < method.Parameters.Length; i++)
            {
                var param = method.Parameters[i];
                var paramSyntax = syntax.ParameterList.Parameters[i];

                var paramInfo = new ParameterInfo
                {
                    Name = param.Name,
                    Type = param.Type.ToDisplayString(),
                    ByRef = param.RefKind != RefKind.None
                };

                // Analyze parameter attributes to determine Harmony parameter type
                AnalyzeParameterAttributes(param, paramInfo);

                parameters.Add(paramInfo);
            }

            return parameters;
        }

        private void AnalyzeParameterAttributes(IParameterSymbol param, ParameterInfo paramInfo)
        {
            foreach (var attr in param.GetAttributes())
            {
                var attrName = attr.AttributeClass?.ToDisplayString();

                paramInfo.ParameterType = attrName switch
                {
                    "TestLE.SourceGenerator.Attributes.InstanceAttribute" => ParameterType.Instance,
                    "TestLE.SourceGenerator.Attributes.ResultAttribute" => ParameterType.Result,
                    "TestLE.SourceGenerator.Attributes.OriginalParameterAttribute" => ParameterType.OriginalParameter,
                    "TestLE.SourceGenerator.Attributes.OriginalMethodAttribute" => ParameterType.OriginalMethod,
                    "TestLE.SourceGenerator.Attributes.RunOriginalAttribute" => ParameterType.RunOriginal,
                    "TestLE.SourceGenerator.Attributes.ExceptionAttribute" => ParameterType.Exception,
                    "TestLE.SourceGenerator.Attributes.StateAttribute" => ParameterType.State,
                    _ => ParameterType.Regular
                };

                // Extract additional parameter information based on attribute type
                if (paramInfo.ParameterType == ParameterType.OriginalParameter)
                {
                    ExtractOriginalParameterInfo(attr, paramInfo);
                }
                else if (paramInfo.ParameterType == ParameterType.Instance)
                {
                    ExtractInstanceCastInfo(attr, paramInfo);
                }
            }
        }

        private void ExtractOriginalParameterInfo(AttributeData attr, ParameterInfo paramInfo)
        {
            // Extract index
            var indexArg = attr.NamedArguments.FirstOrDefault(arg => arg.Key == "Index");
            if (indexArg.Value.Value is int index)
            {
                paramInfo.OriginalParameterIndex = index;
            }

            // Extract name
            var nameArg = attr.NamedArguments.FirstOrDefault(arg => arg.Key == "Name");
            if (nameArg.Value.Value is string name)
            {
                paramInfo.OriginalParameterName = name;
            }

            // Try constructor arguments
            if (attr.ConstructorArguments.Length > 0)
            {
                var firstArg = attr.ConstructorArguments[0];
                if (firstArg.Value is int constructorIndex)
                {
                    paramInfo.OriginalParameterIndex = constructorIndex;
                }
                else if (firstArg.Value is string constructorName)
                {
                    paramInfo.OriginalParameterName = constructorName;
                }
            }
        }

        private void ExtractInstanceCastInfo(AttributeData attr, ParameterInfo paramInfo)
        {
            var castToArg = attr.NamedArguments.FirstOrDefault(arg => arg.Key == "CastTo");
            if (castToArg.Value.Value is INamedTypeSymbol castToType)
            {
                paramInfo.CastToType = castToType.ToDisplayString();
            }

            var castToTypeNameArg = attr.NamedArguments.FirstOrDefault(arg => arg.Key == "CastToTypeName");
            if (castToTypeNameArg.Value.Value is string castToTypeName)
            {
                paramInfo.CastToType = castToTypeName;
            }
        }

        private string GeneratePatchClass(AutoPatchClassInfo classInfo, GeneratorExecutionContext context)
        {
            return CodeGenerator.GeneratePatchClass(classInfo, context);
        }

        private string GenerateRegistrationCode(List<AutoPatchClassInfo> classes)
        {
            return CodeGenerator.GenerateRegistrationCode(classes);
        }
    }
}
