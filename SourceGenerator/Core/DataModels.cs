using System.Collections.Generic;

namespace TestLE.SourceGenerator.Core
{
    public enum PatchType
    {
        Prefix,
        Postfix,
        Transpiler,
        Finalizer
    }

    public enum ParameterType
    {
        Instance,
        Result,
        OriginalParameter,
        OriginalMethod,
        RunOriginal,
        Exception,
        State,
        Regular
    }

    public class AutoPatchClassInfo
    {
        public string ClassName { get; set; } = "";
        public string Namespace { get; set; } = "";
        public string? TargetType { get; set; }
        public int Priority { get; set; } = 400;
        public bool EnableDebugLogging { get; set; } = false;
        public bool ValidateTargetMethods { get; set; } = true;
        public List<PatchMethodInfo> PatchMethods { get; set; } = new List<PatchMethodInfo>();
    }

    public class PatchMethodInfo
    {
        public string MethodName { get; set; } = "";
        public PatchType PatchType { get; set; }
        public string TargetMethodName { get; set; } = "";
        public int Priority { get; set; } = 400;
        public bool IsStatic { get; set; } = false;
        public string ReturnType { get; set; } = "void";
        public List<ParameterInfo> Parameters { get; set; } = new List<ParameterInfo>();
        public string? Condition { get; set; }
        public bool CanSkipOriginal { get; set; } = true; // For prefix patches
    }

    public class ParameterInfo
    {
        public string Name { get; set; } = "";
        public string Type { get; set; } = "";
        public ParameterType ParameterType { get; set; } = ParameterType.Regular;
        public bool ByRef { get; set; } = false;
        public int? OriginalParameterIndex { get; set; }
        public string? OriginalParameterName { get; set; }
        public string? CastToType { get; set; }
    }

    public class TargetMethodInfo
    {
        public string MethodName { get; set; } = "";
        public string DeclaringType { get; set; } = "";
        public bool IsStatic { get; set; } = false;
        public List<string> ParameterTypes { get; set; } = new List<string>();
        public string ReturnType { get; set; } = "void";
        public bool Exists { get; set; } = false;
        public string? ErrorMessage { get; set; }
    }

    public class ValidationResult
    {
        public bool IsValid { get; set; } = true;
        public List<string> Errors { get; set; } = new List<string>();
        public List<string> Warnings { get; set; } = new List<string>();
        public List<string> Suggestions { get; set; } = new List<string>();
    }
}
