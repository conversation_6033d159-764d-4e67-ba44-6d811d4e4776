using Microsoft.CodeAnalysis;
using Microsoft.CodeAnalysis.CSharp.Syntax;
using System;
using System.Collections.Generic;
using System.Linq;

namespace TestLE.SourceGenerator.Core
{
    public static class MethodDiscovery
    {
        public static ValidationResult ValidateTargetMethod(
            string targetTypeName,
            string methodName,
            List<string> parameterTypes,
            Compilation compilation,
            GeneratorExecutionContext context)
        {
            var result = new ValidationResult();

            try
            {
                // Find the target type
                var targetType = FindTypeByName(targetTypeName, compilation);
                if (targetType == null)
                {
                    result.IsValid = false;
                    result.Errors.Add($"Target type '{targetTypeName}' not found. Make sure the type is referenced and accessible.");
                    result.Suggestions.Add($"Check if '{targetTypeName}' is spelled correctly and the assembly is referenced.");
                    return result;
                }

                // Find the target method
                var targetMethods = targetType.GetMembers(methodName)
                    .OfType<IMethodSymbol>()
                    .ToList();

                if (!targetMethods.Any())
                {
                    result.IsValid = false;
                    result.Errors.Add($"Method '{methodName}' not found in type '{targetTypeName}'.");
                    
                    // Suggest similar method names
                    var similarMethods = FindSimilarMethodNames(targetType, methodName);
                    if (similarMethods.Any())
                    {
                        result.Suggestions.Add($"Did you mean one of these methods: {string.Join(", ", similarMethods)}?");
                    }
                    return result;
                }

                // If parameter types are specified, find exact match
                if (parameterTypes.Any())
                {
                    var exactMatch = FindMethodWithParameters(targetMethods, parameterTypes, compilation);
                    if (exactMatch == null)
                    {
                        result.IsValid = false;
                        result.Errors.Add($"Method '{methodName}' with specified parameters not found in type '{targetTypeName}'.");
                        
                        // Show available overloads
                        var overloads = targetMethods.Select(m => FormatMethodSignature(m)).ToList();
                        result.Suggestions.Add($"Available overloads: {string.Join(", ", overloads)}");
                        return result;
                    }
                }
                else if (targetMethods.Count > 1)
                {
                    result.Warnings.Add($"Multiple overloads found for method '{methodName}'. Consider specifying parameter types for clarity.");
                    var overloads = targetMethods.Select(m => FormatMethodSignature(m)).ToList();
                    result.Suggestions.Add($"Available overloads: {string.Join(", ", overloads)}");
                }

                // Additional validations
                ValidateMethodAccessibility(targetMethods.First(), result);
                ValidateMethodCompatibility(targetMethods.First(), result);

            }
            catch (Exception ex)
            {
                result.IsValid = false;
                result.Errors.Add($"Error validating target method: {ex.Message}");
            }

            return result;
        }

        private static INamedTypeSymbol? FindTypeByName(string typeName, Compilation compilation)
        {
            // Try to find the type in the compilation
            var type = compilation.GetTypeByMetadataName(typeName);
            if (type != null)
                return type;

            // Try with Il2Cpp prefix
            type = compilation.GetTypeByMetadataName($"Il2Cpp{typeName}");
            if (type != null)
                return type;

            // Search in all assemblies
            foreach (var assembly in compilation.References)
            {
                var assemblySymbol = compilation.GetAssemblyOrModuleSymbol(assembly) as IAssemblySymbol;
                if (assemblySymbol != null)
                {
                    type = FindTypeInAssembly(assemblySymbol, typeName);
                    if (type != null)
                        return type;
                }
            }

            return null;
        }

        private static INamedTypeSymbol? FindTypeInAssembly(IAssemblySymbol assembly, string typeName)
        {
            return FindTypeInNamespace(assembly.GlobalNamespace, typeName);
        }

        private static INamedTypeSymbol? FindTypeInNamespace(INamespaceSymbol namespaceSymbol, string typeName)
        {
            // Check types in current namespace
            foreach (var type in namespaceSymbol.GetTypeMembers())
            {
                if (type.Name == typeName || type.ToDisplayString() == typeName)
                    return type;
            }

            // Check nested namespaces
            foreach (var nestedNamespace in namespaceSymbol.GetNamespaceMembers())
            {
                var result = FindTypeInNamespace(nestedNamespace, typeName);
                if (result != null)
                    return result;
            }

            return null;
        }

        private static IMethodSymbol? FindMethodWithParameters(
            List<IMethodSymbol> methods,
            List<string> parameterTypes,
            Compilation compilation)
        {
            foreach (var method in methods)
            {
                if (method.Parameters.Length != parameterTypes.Count)
                    continue;

                bool matches = true;
                for (int i = 0; i < method.Parameters.Length; i++)
                {
                    var paramType = method.Parameters[i].Type.ToDisplayString();
                    var expectedType = parameterTypes[i];

                    if (!TypesMatch(paramType, expectedType, compilation))
                    {
                        matches = false;
                        break;
                    }
                }

                if (matches)
                    return method;
            }

            return null;
        }

        private static bool TypesMatch(string actualType, string expectedType, Compilation compilation)
        {
            // Direct match
            if (actualType == expectedType)
                return true;

            // Handle Il2Cpp types
            if (actualType.StartsWith("Il2Cpp") && expectedType == actualType.Substring(6))
                return true;

            if (expectedType.StartsWith("Il2Cpp") && actualType == expectedType.Substring(6))
                return true;

            // Handle generic types and arrays
            // This could be expanded for more complex type matching

            return false;
        }

        private static List<string> FindSimilarMethodNames(INamedTypeSymbol type, string methodName)
        {
            var allMethods = type.GetMembers().OfType<IMethodSymbol>().Select(m => m.Name).Distinct();
            var similar = new List<string>();

            foreach (var name in allMethods)
            {
                if (IsStringSimilar(name, methodName))
                {
                    similar.Add(name);
                }
            }

            return similar.Take(5).ToList(); // Limit to 5 suggestions
        }

        private static bool IsStringSimilar(string str1, string str2)
        {
            // Simple similarity check - could be improved with Levenshtein distance
            if (str1.Length == 0 || str2.Length == 0)
                return false;

            var longer = str1.Length > str2.Length ? str1 : str2;
            var shorter = str1.Length > str2.Length ? str2 : str1;

            if (longer.Length == 0)
                return true;

            var editDistance = ComputeLevenshteinDistance(longer, shorter);
            return (longer.Length - editDistance) / (double)longer.Length >= 0.6; // 60% similarity
        }

        private static int ComputeLevenshteinDistance(string s, string t)
        {
            int n = s.Length;
            int m = t.Length;
            int[,] d = new int[n + 1, m + 1];

            if (n == 0) return m;
            if (m == 0) return n;

            for (int i = 0; i <= n; d[i, 0] = i++) { }
            for (int j = 0; j <= m; d[0, j] = j++) { }

            for (int i = 1; i <= n; i++)
            {
                for (int j = 1; j <= m; j++)
                {
                    int cost = (t[j - 1] == s[i - 1]) ? 0 : 1;
                    d[i, j] = Math.Min(Math.Min(d[i - 1, j] + 1, d[i, j - 1] + 1), d[i - 1, j - 1] + cost);
                }
            }

            return d[n, m];
        }

        private static string FormatMethodSignature(IMethodSymbol method)
        {
            var parameters = method.Parameters.Select(p => $"{p.Type.ToDisplayString()} {p.Name}");
            return $"{method.Name}({string.Join(", ", parameters)})";
        }

        private static void ValidateMethodAccessibility(IMethodSymbol method, ValidationResult result)
        {
            if (method.DeclaredAccessibility == Accessibility.Private)
            {
                result.Warnings.Add($"Method '{method.Name}' is private. Harmony can patch private methods, but consider if this is intended.");
            }
        }

        private static void ValidateMethodCompatibility(IMethodSymbol method, ValidationResult result)
        {
            if (method.IsAbstract)
            {
                result.Warnings.Add($"Method '{method.Name}' is abstract. Patching abstract methods may not work as expected.");
            }

            if (method.IsVirtual && method.IsSealed)
            {
                result.Warnings.Add($"Method '{method.Name}' is sealed virtual. This may affect patch behavior.");
            }
        }
    }
}
