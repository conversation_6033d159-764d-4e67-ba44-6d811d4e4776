using System;

namespace TestLE.SourceGenerator.Attributes
{
    /// <summary>
    /// Marks a class for automatic patch generation. The source generator will analyze
    /// methods in this class and generate corresponding Harmony patches.
    /// </summary>
    [AttributeUsage(AttributeTargets.Class, AllowMultiple = false)]
    public sealed class AutoPatchAttribute : Attribute
    {
        /// <summary>
        /// The target type to patch. If not specified, will be inferred from the class name.
        /// </summary>
        public Type? TargetType { get; set; }

        /// <summary>
        /// Optional namespace prefix for the target type when using string-based targeting.
        /// </summary>
        public string? TargetNamespace { get; set; }

        /// <summary>
        /// The target type name as a string. Useful for Il2Cpp types that may not be directly referenceable.
        /// </summary>
        public string? TargetTypeName { get; set; }

        /// <summary>
        /// Whether to enable debug logging for this patch class.
        /// </summary>
        public bool EnableDebugLogging { get; set; } = false;

        /// <summary>
        /// Priority for patch application. Higher values are applied first.
        /// </summary>
        public int Priority { get; set; } = 400;

        /// <summary>
        /// Whether to validate that all target methods exist at compile time.
        /// </summary>
        public bool ValidateTargetMethods { get; set; } = true;

        public AutoPatchAttribute() { }

        public AutoPatchAttribute(Type targetType)
        {
            TargetType = targetType;
        }

        public AutoPatchAttribute(string targetTypeName)
        {
            TargetTypeName = targetTypeName;
        }
    }
}
