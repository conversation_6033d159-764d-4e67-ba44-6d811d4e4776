using System;

namespace TestLE.SourceGenerator.Attributes
{
    /// <summary>
    /// Marks a parameter as the instance (__instance) in a patch method.
    /// </summary>
    [AttributeUsage(AttributeTargets.Parameter, AllowMultiple = false)]
    public sealed class InstanceAttribute : Attribute
    {
        /// <summary>
        /// Whether to cast the instance to a specific type.
        /// </summary>
        public Type? CastTo { get; set; }

        /// <summary>
        /// Type name to cast to as a string. Useful for Il2Cpp types.
        /// </summary>
        public string? CastToTypeName { get; set; }

        public InstanceAttribute() { }

        public InstanceAttribute(Type castTo)
        {
            CastTo = castTo;
        }

        public InstanceAttribute(string castToTypeName)
        {
            CastToTypeName = castToTypeName;
        }
    }

    /// <summary>
    /// Marks a parameter as the return value (__result) in a patch method.
    /// </summary>
    [AttributeUsage(AttributeTargets.Parameter, AllowMultiple = false)]
    public sealed class ResultAttribute : Attribute
    {
        /// <summary>
        /// Whether this parameter is passed by reference.
        /// </summary>
        public bool ByRef { get; set; } = false;

        public ResultAttribute() { }

        public ResultAttribute(bool byRef)
        {
            ByRef = byRef;
        }
    }

    /// <summary>
    /// Marks a parameter as an original method parameter.
    /// </summary>
    [AttributeUsage(AttributeTargets.Parameter, AllowMultiple = false)]
    public sealed class OriginalParameterAttribute : Attribute
    {
        /// <summary>
        /// The index of the original parameter (0-based).
        /// </summary>
        public int Index { get; set; }

        /// <summary>
        /// The name of the original parameter.
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// Whether this parameter is passed by reference.
        /// </summary>
        public bool ByRef { get; set; } = false;

        public OriginalParameterAttribute(int index)
        {
            Index = index;
        }

        public OriginalParameterAttribute(string name)
        {
            Name = name;
        }
    }

    /// <summary>
    /// Marks a parameter as the original method (__originalMethod).
    /// </summary>
    [AttributeUsage(AttributeTargets.Parameter, AllowMultiple = false)]
    public sealed class OriginalMethodAttribute : Attribute
    {
    }

    /// <summary>
    /// Marks a parameter as the run original flag (__runOriginal) for prefix patches.
    /// </summary>
    [AttributeUsage(AttributeTargets.Parameter, AllowMultiple = false)]
    public sealed class RunOriginalAttribute : Attribute
    {
    }

    /// <summary>
    /// Marks a parameter as the exception (__exception) in finalizer patches.
    /// </summary>
    [AttributeUsage(AttributeTargets.Parameter, AllowMultiple = false)]
    public sealed class ExceptionAttribute : Attribute
    {
    }

    /// <summary>
    /// Marks a parameter as the state object (__state) for sharing data between prefix and postfix.
    /// </summary>
    [AttributeUsage(AttributeTargets.Parameter, AllowMultiple = false)]
    public sealed class StateAttribute : Attribute
    {
        /// <summary>
        /// The type of the state object.
        /// </summary>
        public Type? StateType { get; set; }

        public StateAttribute() { }

        public StateAttribute(Type stateType)
        {
            StateType = stateType;
        }
    }
}
