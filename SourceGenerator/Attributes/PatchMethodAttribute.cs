using System;

namespace TestLE.SourceGenerator.Attributes
{
    /// <summary>
    /// Base attribute for patch methods.
    /// </summary>
    public abstract class PatchMethodAttribute : Attribute
    {
        /// <summary>
        /// The name of the target method to patch. If not specified, will be inferred from the patch method name.
        /// </summary>
        public string? MethodName { get; set; }

        /// <summary>
        /// Parameter types for method overload resolution. Use when targeting overloaded methods.
        /// </summary>
        public Type[]? ParameterTypes { get; set; }

        /// <summary>
        /// Parameter type names as strings. Useful for Il2Cpp types.
        /// </summary>
        public string[]? ParameterTypeNames { get; set; }

        /// <summary>
        /// Whether this method is static.
        /// </summary>
        public bool IsStatic { get; set; } = false;

        /// <summary>
        /// Priority for this specific patch. Higher values are applied first.
        /// </summary>
        public int Priority { get; set; } = 400;

        /// <summary>
        /// Conditions under which this patch should be applied.
        /// </summary>
        public string? Condition { get; set; }

        protected PatchMethodAttribute() { }

        protected PatchMethodAttribute(string methodName)
        {
            MethodName = methodName;
        }
    }

    /// <summary>
    /// Marks a method as a Harmony Prefix patch.
    /// </summary>
    [AttributeUsage(AttributeTargets.Method, AllowMultiple = false)]
    public sealed class PrefixAttribute : PatchMethodAttribute
    {
        /// <summary>
        /// Whether this prefix should skip the original method when returning false.
        /// </summary>
        public bool CanSkipOriginal { get; set; } = true;

        public PrefixAttribute() { }
        public PrefixAttribute(string methodName) : base(methodName) { }
    }

    /// <summary>
    /// Marks a method as a Harmony Postfix patch.
    /// </summary>
    [AttributeUsage(AttributeTargets.Method, AllowMultiple = false)]
    public sealed class PostfixAttribute : PatchMethodAttribute
    {
        public PostfixAttribute() { }
        public PostfixAttribute(string methodName) : base(methodName) { }
    }

    /// <summary>
    /// Marks a method as a Harmony Transpiler patch.
    /// </summary>
    [AttributeUsage(AttributeTargets.Method, AllowMultiple = false)]
    public sealed class TranspilerAttribute : PatchMethodAttribute
    {
        public TranspilerAttribute() { }
        public TranspilerAttribute(string methodName) : base(methodName) { }
    }

    /// <summary>
    /// Marks a method as a Harmony Finalizer patch.
    /// </summary>
    [AttributeUsage(AttributeTargets.Method, AllowMultiple = false)]
    public sealed class FinalizerAttribute : PatchMethodAttribute
    {
        public FinalizerAttribute() { }
        public FinalizerAttribute(string methodName) : base(methodName) { }
    }
}
