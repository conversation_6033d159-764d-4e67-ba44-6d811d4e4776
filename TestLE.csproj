<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net6.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
      <Reference Include="0Harmony">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\net6\0Harmony.dll</HintPath>
      </Reference>
      <Reference Include="AsmResolver">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\net6\AsmResolver.dll</HintPath>
      </Reference>
      <Reference Include="AsmResolver.DotNet">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\net6\AsmResolver.DotNet.dll</HintPath>
      </Reference>
      <Reference Include="AsmResolver.PE">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\net6\AsmResolver.PE.dll</HintPath>
      </Reference>
      <Reference Include="AsmResolver.PE.File">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\net6\AsmResolver.PE.File.dll</HintPath>
      </Reference>
      <Reference Include="AssetRipper.Primitives">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\net6\AssetRipper.Primitives.dll</HintPath>
      </Reference>
      <Reference Include="AssetsTools.NET">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\net6\AssetsTools.NET.dll</HintPath>
      </Reference>
      <Reference Include="bHapticsLib">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\net6\bHapticsLib.dll</HintPath>
      </Reference>
      <Reference Include="Iced">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\net6\Iced.dll</HintPath>
      </Reference>
      <Reference Include="Il2CppInterop.Common">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\net6\Il2CppInterop.Common.dll</HintPath>
      </Reference>
      <Reference Include="Il2CppInterop.Generator">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\net6\Il2CppInterop.Generator.dll</HintPath>
      </Reference>
      <Reference Include="Il2CppInterop.HarmonySupport">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\net6\Il2CppInterop.HarmonySupport.dll</HintPath>
      </Reference>
      <Reference Include="Il2CppInterop.Runtime">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\net6\Il2CppInterop.Runtime.dll</HintPath>
      </Reference>
      <Reference Include="IndexRange">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\net6\IndexRange.dll</HintPath>
      </Reference>
      <Reference Include="MelonLoader">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\net6\MelonLoader.dll</HintPath>
      </Reference>
      <Reference Include="MelonLoader.NativeHost">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\net6\MelonLoader.NativeHost.dll</HintPath>
      </Reference>
      <Reference Include="Microsoft.Bcl.AsyncInterfaces">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\net6\Microsoft.Bcl.AsyncInterfaces.dll</HintPath>
      </Reference>
      <Reference Include="Microsoft.Diagnostics.NETCore.Client">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\net6\Microsoft.Diagnostics.NETCore.Client.dll</HintPath>
      </Reference>
      <Reference Include="Microsoft.Diagnostics.Runtime">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\net6\Microsoft.Diagnostics.Runtime.dll</HintPath>
      </Reference>
      <Reference Include="Microsoft.Extensions.Configuration">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\net6\Microsoft.Extensions.Configuration.dll</HintPath>
      </Reference>
      <Reference Include="Microsoft.Extensions.Configuration.Abstractions">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\net6\Microsoft.Extensions.Configuration.Abstractions.dll</HintPath>
      </Reference>
      <Reference Include="Microsoft.Extensions.Configuration.Binder">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\net6\Microsoft.Extensions.Configuration.Binder.dll</HintPath>
      </Reference>
      <Reference Include="Microsoft.Extensions.DependencyInjection.Abstractions">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\net6\Microsoft.Extensions.DependencyInjection.Abstractions.dll</HintPath>
      </Reference>
      <Reference Include="Microsoft.Extensions.Logging">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\net6\Microsoft.Extensions.Logging.dll</HintPath>
      </Reference>
      <Reference Include="Microsoft.Extensions.Logging.Abstractions">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\net6\Microsoft.Extensions.Logging.Abstractions.dll</HintPath>
      </Reference>
      <Reference Include="Microsoft.Extensions.Options">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\net6\Microsoft.Extensions.Options.dll</HintPath>
      </Reference>
      <Reference Include="Microsoft.Extensions.Primitives">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\net6\Microsoft.Extensions.Primitives.dll</HintPath>
      </Reference>
      <Reference Include="Mono.Cecil">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\net6\Mono.Cecil.dll</HintPath>
      </Reference>
      <Reference Include="Mono.Cecil.Mdb">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\net6\Mono.Cecil.Mdb.dll</HintPath>
      </Reference>
      <Reference Include="Mono.Cecil.Pdb">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\net6\Mono.Cecil.Pdb.dll</HintPath>
      </Reference>
      <Reference Include="Mono.Cecil.Rocks">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\net6\Mono.Cecil.Rocks.dll</HintPath>
      </Reference>
      <Reference Include="MonoMod">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\net6\MonoMod.dll</HintPath>
      </Reference>
      <Reference Include="MonoMod.Backports">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\net6\MonoMod.Backports.dll</HintPath>
      </Reference>
      <Reference Include="MonoMod.ILHelpers">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\net6\MonoMod.ILHelpers.dll</HintPath>
      </Reference>
      <Reference Include="MonoMod.RuntimeDetour">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\net6\MonoMod.RuntimeDetour.dll</HintPath>
      </Reference>
      <Reference Include="MonoMod.Utils">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\net6\MonoMod.Utils.dll</HintPath>
      </Reference>
      <Reference Include="Newtonsoft.Json">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\net6\Newtonsoft.Json.dll</HintPath>
      </Reference>
      <Reference Include="System.Configuration.ConfigurationManager">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\net6\System.Configuration.ConfigurationManager.dll</HintPath>
      </Reference>
      <Reference Include="System.Security.Cryptography.ProtectedData">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\net6\System.Security.Cryptography.ProtectedData.dll</HintPath>
      </Reference>
      <Reference Include="System.Security.Permissions">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\net6\System.Security.Permissions.dll</HintPath>
      </Reference>
      <Reference Include="System.Windows.Extensions">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\net6\System.Windows.Extensions.dll</HintPath>
      </Reference>
      <Reference Include="Tomlet">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\net6\Tomlet.dll</HintPath>
      </Reference>
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="SourceGenerator\TestLE.SourceGenerator.csproj" OutputItemType="Analyzer" ReferenceOutputAssembly="false" />
    </ItemGroup>

    <ItemGroup>
      <Reference Include="UnityEngine.Il2CppAssetBundleManager">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\net6\UnityEngine.Il2CppAssetBundleManager.dll</HintPath>
      </Reference>
      <Reference Include="UnityEngine.Il2CppImageConversionManager">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\net6\UnityEngine.Il2CppImageConversionManager.dll</HintPath>
      </Reference>
      <Reference Include="WebSocketDotNet">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\net6\WebSocketDotNet.dll</HintPath>
      </Reference>
      
      <Reference Include="Il2Cpp">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Dependencies\SupportModules\Il2Cpp.dll</HintPath>
      </Reference>
      <Reference Include="Il2CppALINE">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\Il2CppALINE.dll</HintPath>
      </Reference>
      <Reference Include="Il2CppAwesomeTechnologies.TouchReactSystemPro.Runtime">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\Il2CppAwesomeTechnologies.TouchReactSystemPro.Runtime.dll</HintPath>
      </Reference>
      <Reference Include="Il2CppAwesomeTechnologies.VegetationStudioPro.Runtime">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\Il2CppAwesomeTechnologies.VegetationStudioPro.Runtime.dll</HintPath>
      </Reference>
      <Reference Include="Il2CppBezierSolution.Runtime">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\Il2CppBezierSolution.Runtime.dll</HintPath>
      </Reference>
      <Reference Include="Il2CppCinemachine">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\Il2CppCinemachine.dll</HintPath>
      </Reference>
      <Reference Include="Il2CppCommandLine">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\Il2CppCommandLine.dll</HintPath>
      </Reference>
      <Reference Include="Il2CppCrest">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\Il2CppCrest.dll</HintPath>
      </Reference>
      <Reference Include="Il2Cppcrosstales">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\Il2Cppcrosstales.dll</HintPath>
      </Reference>
      <Reference Include="Il2CppCSharp.OperationResult">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\Il2CppCSharp.OperationResult.dll</HintPath>
      </Reference>
      <Reference Include="Il2CppDialogueSystem">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\Il2CppDialogueSystem.dll</HintPath>
      </Reference>
      <Reference Include="Il2CppDOTween">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\Il2CppDOTween.dll</HintPath>
      </Reference>
      <Reference Include="Il2CppDOTweenPro">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\Il2CppDOTweenPro.dll</HintPath>
      </Reference>
      <Reference Include="Il2CppDunGen">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\Il2CppDunGen.dll</HintPath>
      </Reference>
      <Reference Include="Il2CppEHRGB">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\Il2CppEHRGB.dll</HintPath>
      </Reference>
      <Reference Include="Il2CppFacepunch.Steamworks.Win64">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\Il2CppFacepunch.Steamworks.Win64.dll</HintPath>
      </Reference>
      <Reference Include="Il2CppFmodShared">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\Il2CppFmodShared.dll</HintPath>
      </Reference>
      <Reference Include="Il2CppFMODUnity">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\Il2CppFMODUnity.dll</HintPath>
      </Reference>
      <Reference Include="Il2CppFMODUnityResonance">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\Il2CppFMODUnityResonance.dll</HintPath>
      </Reference>
      <Reference Include="Il2CppGenerated">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\Il2CppGenerated.dll</HintPath>
      </Reference>
      <Reference Include="Il2CppHeathen.ScreenKeyboard">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\Il2CppHeathen.ScreenKeyboard.dll</HintPath>
      </Reference>
      <Reference Include="Il2CppHoudiniEngineUnity">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\Il2CppHoudiniEngineUnity.dll</HintPath>
      </Reference>
      <Reference Include="Il2CppJBooth.MicroSplat.Core">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\Il2CppJBooth.MicroSplat.Core.dll</HintPath>
      </Reference>
      <Reference Include="Il2CppLE">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\Il2CppLE.dll</HintPath>
      </Reference>
      <Reference Include="Il2CppLE.Core">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\Il2CppLE.Core.dll</HintPath>
      </Reference>
      <Reference Include="Il2CppLE.Networking.Core">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\Il2CppLE.Networking.Core.dll</HintPath>
      </Reference>
      <Reference Include="Il2CppLE.PCG">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\Il2CppLE.PCG.dll</HintPath>
      </Reference>
      <Reference Include="Il2CppLE.Telemetry">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\Il2CppLE.Telemetry.dll</HintPath>
      </Reference>
      <Reference Include="Il2CppLE.Telemetry.Client">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\Il2CppLE.Telemetry.Client.dll</HintPath>
      </Reference>
      <Reference Include="Il2CppLE.UI.Controls">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\Il2CppLE.UI.Controls.dll</HintPath>
      </Reference>
      <Reference Include="Il2CppLidgren.Network">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\Il2CppLidgren.Network.dll</HintPath>
      </Reference>
      <Reference Include="Il2CppLuaInterpreter">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\Il2CppLuaInterpreter.dll</HintPath>
      </Reference>
      <Reference Include="Il2CppMathNet.Numerics">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\Il2CppMathNet.Numerics.dll</HintPath>
      </Reference>
      <Reference Include="Il2CppMeshExtension">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\Il2CppMeshExtension.dll</HintPath>
      </Reference>
      <Reference Include="Il2CppMono.Security">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\Il2CppMono.Security.dll</HintPath>
      </Reference>
      <Reference Include="Il2Cppmscorlib">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\Il2Cppmscorlib.dll</HintPath>
      </Reference>
      <Reference Include="Il2CppNewAssembly">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\Il2CppNewAssembly.dll</HintPath>
      </Reference>
      <Reference Include="Il2CppNewtonsoft.Json">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\Il2CppNewtonsoft.Json.dll</HintPath>
      </Reference>
      <Reference Include="Il2CppNewtonsoft.Json.UnityConverters">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\Il2CppNewtonsoft.Json.UnityConverters.dll</HintPath>
      </Reference>
      <Reference Include="Il2CppPlayFab">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\Il2CppPlayFab.dll</HintPath>
      </Reference>
      <Reference Include="Il2CppPolly">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\Il2CppPolly.dll</HintPath>
      </Reference>
      <Reference Include="Il2CppProBuilderCore-Unity5">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\Il2CppProBuilderCore-Unity5.dll</HintPath>
      </Reference>
      <Reference Include="Il2CppProBuilderMeshOps-Unity5">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\Il2CppProBuilderMeshOps-Unity5.dll</HintPath>
      </Reference>
      <Reference Include="Il2CppRewired_Core">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\Il2CppRewired_Core.dll</HintPath>
      </Reference>
      <Reference Include="Il2CppRewired_Windows">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\Il2CppRewired_Windows.dll</HintPath>
      </Reference>
      <Reference Include="Il2CppRewired_Windows_Functions">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\Il2CppRewired_Windows_Functions.dll</HintPath>
      </Reference>
      <Reference Include="Il2CppRG.ImGui">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\Il2CppRG.ImGui.dll</HintPath>
      </Reference>
      <Reference Include="Il2CppRG.ImGui.Unity">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\Il2CppRG.ImGui.Unity.dll</HintPath>
      </Reference>
      <Reference Include="Il2CppSirenix.OdinInspector.Attributes">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\Il2CppSirenix.OdinInspector.Attributes.dll</HintPath>
      </Reference>
      <Reference Include="Il2CppSirenix.OdinInspector.Modules.Unity.Addressables">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\Il2CppSirenix.OdinInspector.Modules.Unity.Addressables.dll</HintPath>
      </Reference>
      <Reference Include="Il2CppSirenix.OdinInspector.Modules.UnityLocalization">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\Il2CppSirenix.OdinInspector.Modules.UnityLocalization.dll</HintPath>
      </Reference>
      <Reference Include="Il2CppSirenix.Serialization">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\Il2CppSirenix.Serialization.dll</HintPath>
      </Reference>
      <Reference Include="Il2CppSirenix.Serialization.AOTGenerated">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\Il2CppSirenix.Serialization.AOTGenerated.dll</HintPath>
      </Reference>
      <Reference Include="Il2CppSirenix.Serialization.Config">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\Il2CppSirenix.Serialization.Config.dll</HintPath>
      </Reference>
      <Reference Include="Il2CppSirenix.Utilities">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\Il2CppSirenix.Utilities.dll</HintPath>
      </Reference>
      <Reference Include="Il2CppStreamChat.Core">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\Il2CppStreamChat.Core.dll</HintPath>
      </Reference>
      <Reference Include="Il2CppStreamChat.Libs">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\Il2CppStreamChat.Libs.dll</HintPath>
      </Reference>
      <Reference Include="Il2CppSystem">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\Il2CppSystem.dll</HintPath>
      </Reference>
      <Reference Include="Il2CppSystem.ComponentModel.DataAnnotations">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\Il2CppSystem.ComponentModel.DataAnnotations.dll</HintPath>
      </Reference>
      <Reference Include="Il2CppSystem.Configuration">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\Il2CppSystem.Configuration.dll</HintPath>
      </Reference>
      <Reference Include="Il2CppSystem.Core">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\Il2CppSystem.Core.dll</HintPath>
      </Reference>
      <Reference Include="Il2CppSystem.Data">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\Il2CppSystem.Data.dll</HintPath>
      </Reference>
      <Reference Include="Il2CppSystem.Drawing">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\Il2CppSystem.Drawing.dll</HintPath>
      </Reference>
      <Reference Include="Il2CppSystem.IO.Compression">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\Il2CppSystem.IO.Compression.dll</HintPath>
      </Reference>
      <Reference Include="Il2CppSystem.IO.Compression.FileSystem">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\Il2CppSystem.IO.Compression.FileSystem.dll</HintPath>
      </Reference>
      <Reference Include="Il2CppSystem.Net.Http">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\Il2CppSystem.Net.Http.dll</HintPath>
      </Reference>
      <Reference Include="Il2CppSystem.Numerics">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\Il2CppSystem.Numerics.dll</HintPath>
      </Reference>
      <Reference Include="Il2CppSystem.Runtime.CompilerServices.Unsafe">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\Il2CppSystem.Runtime.CompilerServices.Unsafe.dll</HintPath>
      </Reference>
      <Reference Include="Il2CppSystem.Runtime.Serialization">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\Il2CppSystem.Runtime.Serialization.dll</HintPath>
      </Reference>
      <Reference Include="Il2CppSystem.Xml">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\Il2CppSystem.Xml.dll</HintPath>
      </Reference>
      <Reference Include="Il2CppSystem.Xml.Linq">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\Il2CppSystem.Xml.Linq.dll</HintPath>
      </Reference>
      <Reference Include="Il2CppTriangleNET">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\Il2CppTriangleNET.dll</HintPath>
      </Reference>
      <Reference Include="Il2CppUMA_Content">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\Il2CppUMA_Content.dll</HintPath>
      </Reference>
      <Reference Include="Il2CppUMA_Core">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\Il2CppUMA_Core.dll</HintPath>
      </Reference>
      <Reference Include="Il2CppUMA_Examples">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\Il2CppUMA_Examples.dll</HintPath>
      </Reference>
      <Reference Include="Il2CppUniTask">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\Il2CppUniTask.dll</HintPath>
      </Reference>
      <Reference Include="Il2CppUniTask.Addressables">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\Il2CppUniTask.Addressables.dll</HintPath>
      </Reference>
      <Reference Include="Il2CppXNode">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\Il2CppXNode.dll</HintPath>
      </Reference>
      <Reference Include="Il2Cpp__Generated">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\Il2Cpp__Generated.dll</HintPath>
      </Reference>
      <Reference Include="Unity.Addressables">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\Unity.Addressables.dll</HintPath>
      </Reference>
      <Reference Include="Unity.AI.Navigation">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\Unity.AI.Navigation.dll</HintPath>
      </Reference>
      <Reference Include="Unity.Burst">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\Unity.Burst.dll</HintPath>
      </Reference>
      <Reference Include="Unity.Burst.Unsafe">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\Unity.Burst.Unsafe.dll</HintPath>
      </Reference>
      <Reference Include="Unity.Collections">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\Unity.Collections.dll</HintPath>
      </Reference>
      <Reference Include="Unity.Collections.LowLevel.ILSupport">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\Unity.Collections.LowLevel.ILSupport.dll</HintPath>
      </Reference>
      <Reference Include="Unity.Localization">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\Unity.Localization.dll</HintPath>
      </Reference>
      <Reference Include="Unity.Mathematics">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\Unity.Mathematics.dll</HintPath>
      </Reference>
      <Reference Include="Unity.MemoryProfiler">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\Unity.MemoryProfiler.dll</HintPath>
      </Reference>
      <Reference Include="Unity.Postprocessing.Runtime">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\Unity.Postprocessing.Runtime.dll</HintPath>
      </Reference>
      <Reference Include="Unity.Profiling.Core">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\Unity.Profiling.Core.dll</HintPath>
      </Reference>
      <Reference Include="Unity.RenderPipelines.Core.Runtime">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\Unity.RenderPipelines.Core.Runtime.dll</HintPath>
      </Reference>
      <Reference Include="Unity.ResourceManager">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\Unity.ResourceManager.dll</HintPath>
      </Reference>
      <Reference Include="Unity.Services.Analytics">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\Unity.Services.Analytics.dll</HintPath>
      </Reference>
      <Reference Include="Unity.Services.Core">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\Unity.Services.Core.dll</HintPath>
      </Reference>
      <Reference Include="Unity.Services.Core.Configuration">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\Unity.Services.Core.Configuration.dll</HintPath>
      </Reference>
      <Reference Include="Unity.Services.Core.Device">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\Unity.Services.Core.Device.dll</HintPath>
      </Reference>
      <Reference Include="Unity.Services.Core.Environments">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\Unity.Services.Core.Environments.dll</HintPath>
      </Reference>
      <Reference Include="Unity.Services.Core.Environments.Internal">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\Unity.Services.Core.Environments.Internal.dll</HintPath>
      </Reference>
      <Reference Include="Unity.Services.Core.Internal">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\Unity.Services.Core.Internal.dll</HintPath>
      </Reference>
      <Reference Include="Unity.Services.Core.Registration">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\Unity.Services.Core.Registration.dll</HintPath>
      </Reference>
      <Reference Include="Unity.Services.Core.Scheduler">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\Unity.Services.Core.Scheduler.dll</HintPath>
      </Reference>
      <Reference Include="Unity.Services.Core.Telemetry">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\Unity.Services.Core.Telemetry.dll</HintPath>
      </Reference>
      <Reference Include="Unity.Services.Core.Threading">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\Unity.Services.Core.Threading.dll</HintPath>
      </Reference>
      <Reference Include="Unity.TextMeshPro">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\Unity.TextMeshPro.dll</HintPath>
      </Reference>
      <Reference Include="Unity.Timeline">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\Unity.Timeline.dll</HintPath>
      </Reference>
      <Reference Include="UnityEngine">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\UnityEngine.dll</HintPath>
      </Reference>
      <Reference Include="UnityEngine.AccessibilityModule">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\UnityEngine.AccessibilityModule.dll</HintPath>
      </Reference>
      <Reference Include="UnityEngine.AIModule">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\UnityEngine.AIModule.dll</HintPath>
      </Reference>
      <Reference Include="UnityEngine.AndroidJNIModule">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\UnityEngine.AndroidJNIModule.dll</HintPath>
      </Reference>
      <Reference Include="UnityEngine.AnimationModule">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\UnityEngine.AnimationModule.dll</HintPath>
      </Reference>
      <Reference Include="UnityEngine.ARModule">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\UnityEngine.ARModule.dll</HintPath>
      </Reference>
      <Reference Include="UnityEngine.AssetBundleModule">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\UnityEngine.AssetBundleModule.dll</HintPath>
      </Reference>
      <Reference Include="UnityEngine.AudioModule">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\UnityEngine.AudioModule.dll</HintPath>
      </Reference>
      <Reference Include="UnityEngine.ClothModule">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\UnityEngine.ClothModule.dll</HintPath>
      </Reference>
      <Reference Include="UnityEngine.ContentLoadModule">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\UnityEngine.ContentLoadModule.dll</HintPath>
      </Reference>
      <Reference Include="UnityEngine.CoreModule">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\UnityEngine.CoreModule.dll</HintPath>
      </Reference>
      <Reference Include="UnityEngine.CrashReportingModule">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\UnityEngine.CrashReportingModule.dll</HintPath>
      </Reference>
      <Reference Include="UnityEngine.DirectorModule">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\UnityEngine.DirectorModule.dll</HintPath>
      </Reference>
      <Reference Include="UnityEngine.DSPGraphModule">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\UnityEngine.DSPGraphModule.dll</HintPath>
      </Reference>
      <Reference Include="UnityEngine.GameCenterModule">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\UnityEngine.GameCenterModule.dll</HintPath>
      </Reference>
      <Reference Include="UnityEngine.GIModule">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\UnityEngine.GIModule.dll</HintPath>
      </Reference>
      <Reference Include="UnityEngine.GraphicsStateCollectionSerializerModule">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\UnityEngine.GraphicsStateCollectionSerializerModule.dll</HintPath>
      </Reference>
      <Reference Include="UnityEngine.GridModule">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\UnityEngine.GridModule.dll</HintPath>
      </Reference>
      <Reference Include="UnityEngine.HierarchyCoreModule">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\UnityEngine.HierarchyCoreModule.dll</HintPath>
      </Reference>
      <Reference Include="UnityEngine.HotReloadModule">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\UnityEngine.HotReloadModule.dll</HintPath>
      </Reference>
      <Reference Include="UnityEngine.ImageConversionModule">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\UnityEngine.ImageConversionModule.dll</HintPath>
      </Reference>
      <Reference Include="UnityEngine.IMGUIModule">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\UnityEngine.IMGUIModule.dll</HintPath>
      </Reference>
      <Reference Include="UnityEngine.InputForUIModule">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\UnityEngine.InputForUIModule.dll</HintPath>
      </Reference>
      <Reference Include="UnityEngine.InputLegacyModule">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\UnityEngine.InputLegacyModule.dll</HintPath>
      </Reference>
      <Reference Include="UnityEngine.InputModule">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\UnityEngine.InputModule.dll</HintPath>
      </Reference>
      <Reference Include="UnityEngine.JSONSerializeModule">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\UnityEngine.JSONSerializeModule.dll</HintPath>
      </Reference>
      <Reference Include="UnityEngine.LocalizationModule">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\UnityEngine.LocalizationModule.dll</HintPath>
      </Reference>
      <Reference Include="UnityEngine.MarshallingModule">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\UnityEngine.MarshallingModule.dll</HintPath>
      </Reference>
      <Reference Include="UnityEngine.MultiplayerModule">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\UnityEngine.MultiplayerModule.dll</HintPath>
      </Reference>
      <Reference Include="UnityEngine.ParticleSystemModule">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\UnityEngine.ParticleSystemModule.dll</HintPath>
      </Reference>
      <Reference Include="UnityEngine.PerformanceReportingModule">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\UnityEngine.PerformanceReportingModule.dll</HintPath>
      </Reference>
      <Reference Include="UnityEngine.Physics2DModule">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\UnityEngine.Physics2DModule.dll</HintPath>
      </Reference>
      <Reference Include="UnityEngine.PhysicsModule">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\UnityEngine.PhysicsModule.dll</HintPath>
      </Reference>
      <Reference Include="UnityEngine.PropertiesModule">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\UnityEngine.PropertiesModule.dll</HintPath>
      </Reference>
      <Reference Include="UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll</HintPath>
      </Reference>
      <Reference Include="UnityEngine.ScreenCaptureModule">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\UnityEngine.ScreenCaptureModule.dll</HintPath>
      </Reference>
      <Reference Include="UnityEngine.ShaderVariantAnalyticsModule">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\UnityEngine.ShaderVariantAnalyticsModule.dll</HintPath>
      </Reference>
      <Reference Include="UnityEngine.SharedInternalsModule">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\UnityEngine.SharedInternalsModule.dll</HintPath>
      </Reference>
      <Reference Include="UnityEngine.SpriteMaskModule">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\UnityEngine.SpriteMaskModule.dll</HintPath>
      </Reference>
      <Reference Include="UnityEngine.SpriteShapeModule">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\UnityEngine.SpriteShapeModule.dll</HintPath>
      </Reference>
      <Reference Include="UnityEngine.StreamingModule">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\UnityEngine.StreamingModule.dll</HintPath>
      </Reference>
      <Reference Include="UnityEngine.SubstanceModule">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\UnityEngine.SubstanceModule.dll</HintPath>
      </Reference>
      <Reference Include="UnityEngine.SubsystemsModule">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\UnityEngine.SubsystemsModule.dll</HintPath>
      </Reference>
      <Reference Include="UnityEngine.TerrainModule">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\UnityEngine.TerrainModule.dll</HintPath>
      </Reference>
      <Reference Include="UnityEngine.TerrainPhysicsModule">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\UnityEngine.TerrainPhysicsModule.dll</HintPath>
      </Reference>
      <Reference Include="UnityEngine.TextCoreFontEngineModule">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\UnityEngine.TextCoreFontEngineModule.dll</HintPath>
      </Reference>
      <Reference Include="UnityEngine.TextCoreTextEngineModule">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\UnityEngine.TextCoreTextEngineModule.dll</HintPath>
      </Reference>
      <Reference Include="UnityEngine.TextRenderingModule">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\UnityEngine.TextRenderingModule.dll</HintPath>
      </Reference>
      <Reference Include="UnityEngine.TilemapModule">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\UnityEngine.TilemapModule.dll</HintPath>
      </Reference>
      <Reference Include="UnityEngine.TLSModule">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\UnityEngine.TLSModule.dll</HintPath>
      </Reference>
      <Reference Include="UnityEngine.UI">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\UnityEngine.UI.dll</HintPath>
      </Reference>
      <Reference Include="UnityEngine.UIElementsModule">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\UnityEngine.UIElementsModule.dll</HintPath>
      </Reference>
      <Reference Include="UnityEngine.UIModule">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\UnityEngine.UIModule.dll</HintPath>
      </Reference>
      <Reference Include="UnityEngine.UmbraModule">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\UnityEngine.UmbraModule.dll</HintPath>
      </Reference>
      <Reference Include="UnityEngine.UnityAnalyticsCommonModule">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\UnityEngine.UnityAnalyticsCommonModule.dll</HintPath>
      </Reference>
      <Reference Include="UnityEngine.UnityAnalyticsModule">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\UnityEngine.UnityAnalyticsModule.dll</HintPath>
      </Reference>
      <Reference Include="UnityEngine.UnityConnectModule">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\UnityEngine.UnityConnectModule.dll</HintPath>
      </Reference>
      <Reference Include="UnityEngine.UnityCurlModule">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\UnityEngine.UnityCurlModule.dll</HintPath>
      </Reference>
      <Reference Include="UnityEngine.UnityTestProtocolModule">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\UnityEngine.UnityTestProtocolModule.dll</HintPath>
      </Reference>
      <Reference Include="UnityEngine.UnityWebRequestAssetBundleModule">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\UnityEngine.UnityWebRequestAssetBundleModule.dll</HintPath>
      </Reference>
      <Reference Include="UnityEngine.UnityWebRequestAudioModule">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\UnityEngine.UnityWebRequestAudioModule.dll</HintPath>
      </Reference>
      <Reference Include="UnityEngine.UnityWebRequestModule">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\UnityEngine.UnityWebRequestModule.dll</HintPath>
      </Reference>
      <Reference Include="UnityEngine.UnityWebRequestTextureModule">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\UnityEngine.UnityWebRequestTextureModule.dll</HintPath>
      </Reference>
      <Reference Include="UnityEngine.UnityWebRequestWWWModule">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\UnityEngine.UnityWebRequestWWWModule.dll</HintPath>
      </Reference>
      <Reference Include="UnityEngine.VehiclesModule">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\UnityEngine.VehiclesModule.dll</HintPath>
      </Reference>
      <Reference Include="UnityEngine.VFXModule">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\UnityEngine.VFXModule.dll</HintPath>
      </Reference>
      <Reference Include="UnityEngine.VideoModule">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\UnityEngine.VideoModule.dll</HintPath>
      </Reference>
      <Reference Include="UnityEngine.VRModule">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\UnityEngine.VRModule.dll</HintPath>
      </Reference>
      <Reference Include="UnityEngine.WindModule">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\UnityEngine.WindModule.dll</HintPath>
      </Reference>
      <Reference Include="UnityEngine.XRModule">
        <HintPath>H:\SteamLibrary\steamapps\common\Last Epoch - Copy\MelonLoader\Il2CppAssemblies\UnityEngine.XRModule.dll</HintPath>
      </Reference>
    </ItemGroup>

  <Target Name="PostBuild" AfterTargets="Build">
    <Copy SourceFiles="$(OutputPath)$(AssemblyName).dll" DestinationFolder="H:\SteamLibrary\steamapps\common\Last Epoch - Copy\Mods\" />
  </Target>
  
</Project>
